# Adding New Charts - Step-by-Step Example

## Overview
This guide demonstrates how to add new chart types to the refactored dashboard service using the dynamic configuration system.

## 🎯 Example: Adding a "Performance Metrics" Line Chart

### **Step 1: Add New Chart Type Enum**
```typescript
// In dashboard.service.ts
enum ChartType {
    ONBOARDING_LINE = 'onboarding_line',
    ONBOARDING_BAR = 'onboarding_bar',
    CONTRACT_PIE = 'contract_pie',
    LEAVE_COMPARISON = 'leave_comparison',
    // ✅ ADD NEW CHART TYPE
    PERFORMANCE_METRICS = 'performance_metrics'
}
```

### **Step 2: Create Data Fetching Function**
```typescript
/**
 * Get performance metrics data for line chart (Recharts format)
 */
const getPerformanceMetricsData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `performance_metrics_${organizationId}_${filters?.branchId || 'all'}`;
    
    return getCachedData(cacheKey, async () => {
        try {
            const { monthKeys, monthLabels } = generateMonthRange(6);
            
            // Build where clause
            const whereClause: any = {
                organization_id: organizationId
            };

            if (filters?.branchId) {
                whereClause.branch_id = filters.branchId;
            }

            // Fetch performance data (example query)
            const performanceData = await User.findAll({
                where: whereClause,
                attributes: ['id', 'performance_score', 'createdAt'],
                raw: true,
                order: [['createdAt', 'ASC']]
            });

            // Process data for Recharts
            const performanceByMonth = new Map<string, { total: number; count: number }>();
            monthKeys.forEach(key => performanceByMonth.set(key, { total: 0, count: 0 }));

            performanceData.forEach((user: any) => {
                if (user.createdAt && user.performance_score) {
                    const monthKey = DateUtils.toMonthKey(user.createdAt);
                    if (performanceByMonth.has(monthKey)) {
                        const current = performanceByMonth.get(monthKey)!;
                        current.total += user.performance_score;
                        current.count += 1;
                    }
                }
            });

            // Calculate average performance per month
            const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => {
                const monthData = performanceByMonth.get(monthKeys[index]);
                const average = monthData && monthData.count > 0 
                    ? Math.round((monthData.total / monthData.count) * 100) / 100 
                    : 0;

                return {
                    name: label,
                    value: average,
                    performance: average,
                    month: label
                };
            });

            return {
                success: true,
                chartType: 'line',
                title: 'Performance Metrics Trend',
                data: {
                    data: chartData,
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: [{
                        dataKey: 'performance',
                        name: 'Average Performance Score',
                        color: '#8B5CF6',
                        type: 'line'
                    }]
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: performanceData.length,
                    dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getPerformanceMetricsData:', error);
            return {
                success: false,
                chartType: 'line',
                title: 'Performance Metrics Trend',
                data: {
                    data: [],
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: []
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};
```

### **Step 3: Add Configuration Entry**
```typescript
// In CHART_CONFIGURATIONS mapping
const CHART_CONFIGURATIONS: Record<ChartType, ChartConfig> = {
    [ChartType.ONBOARDING_LINE]: {
        // ... existing configuration
    },
    [ChartType.ONBOARDING_BAR]: {
        // ... existing configuration
    },
    [ChartType.CONTRACT_PIE]: {
        // ... existing configuration
    },
    [ChartType.LEAVE_COMPARISON]: {
        // ... existing configuration
    },
    // ✅ ADD NEW CHART CONFIGURATION
    [ChartType.PERFORMANCE_METRICS]: {
        type: ChartType.PERFORMANCE_METRICS,
        widgetType: 'line_chart',
        title: 'Performance Metrics Trend',
        chartType: 'line',
        category: 'chart',
        description: 'Shows average performance scores over time by month',
        hasFilters: true,
        dataFetcher: getPerformanceMetricsData
    }
};
```

### **Step 4: That's It! 🎉**
The new chart is automatically:
- ✅ Included in API responses
- ✅ Filtered correctly based on user preferences
- ✅ Cached for performance
- ✅ Error handled gracefully
- ✅ Ordered correctly in the dashboard

## 🔄 Example: Adding a "Department Productivity" Bar Chart

### **Complete Implementation**
```typescript
// 1. Add enum
enum ChartType {
    // ... existing types
    DEPARTMENT_PRODUCTIVITY = 'department_productivity'
}

// 2. Create data fetcher
const getDepartmentProductivityData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    const cacheKey = `dept_productivity_${organizationId}_${filters?.branchId || 'all'}`;
    
    return getCachedData(cacheKey, async () => {
        try {
            // Fetch department productivity data
            const productivityData = await Department.findAll({
                where: { organization_id: organizationId },
                include: [{
                    model: User,
                    attributes: ['productivity_score']
                }],
                attributes: ['id', 'department_name'],
                raw: false
            });

            // Process data for bar chart
            const chartData: RechartsDataPoint[] = productivityData.map((dept: any) => {
                const scores = dept.Users?.map((u: any) => u.productivity_score).filter(Boolean) || [];
                const avgScore = scores.length > 0 
                    ? scores.reduce((a: number, b: number) => a + b, 0) / scores.length 
                    : 0;

                return {
                    name: dept.department_name,
                    value: Math.round(avgScore * 100) / 100,
                    productivity: Math.round(avgScore * 100) / 100,
                    department: dept.department_name,
                    userCount: scores.length
                };
            });

            return {
                success: true,
                chartType: 'bar',
                title: 'Department Productivity Overview',
                data: {
                    data: chartData,
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: [{
                        dataKey: 'productivity',
                        name: 'Average Productivity Score',
                        color: '#10B981',
                        type: 'bar'
                    }]
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: chartData.length,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getDepartmentProductivityData:', error);
            return {
                success: false,
                chartType: 'bar',
                title: 'Department Productivity Overview',
                data: { data: [], xAxisKey: 'name', yAxisKey: 'value', series: [] } as RechartsLineBarData,
                metadata: { totalRecords: 0, lastUpdated: DateUtils.now() }
            };
        }
    });
};

// 3. Add configuration
[ChartType.DEPARTMENT_PRODUCTIVITY]: {
    type: ChartType.DEPARTMENT_PRODUCTIVITY,
    widgetType: 'bar_chart',
    title: 'Department Productivity Overview',
    chartType: 'bar',
    category: 'chart',
    description: 'Compare average productivity scores across departments',
    hasFilters: false,
    dataFetcher: getDepartmentProductivityData
}
```

## 🎛️ Advanced Configuration Options

### **Conditional Chart Display**
```typescript
// Add conditional properties to ChartConfig interface
interface ChartConfig {
    // ... existing properties
    requiresPermission?: string;
    isExperimental?: boolean;
    minUserRole?: 'user' | 'admin' | 'super_admin';
}

// Update filtering logic
const getFilteredChartConfigs = (filters?: FilterOptions): ChartConfig[] => {
    let configs = Object.values(CHART_CONFIGURATIONS);
    
    // Filter by user role
    if (filters?.userRole) {
        configs = configs.filter(config => {
            if (!config.minUserRole) return true;
            const roleHierarchy = { user: 1, admin: 2, super_admin: 3 };
            return roleHierarchy[filters.userRole] >= roleHierarchy[config.minUserRole];
        });
    }
    
    // Filter experimental charts
    if (!filters?.showExperimental) {
        configs = configs.filter(config => !config.isExperimental);
    }
    
    return configs.sort((a, b) => a.order - b.order);
};
```

### **Dynamic Chart Titles**
```typescript
// Add dynamic title function
interface ChartConfig {
    // ... existing properties
    titleGenerator?: (filters?: FilterOptions) => string;
}

// Example configuration with dynamic title
[ChartType.PERFORMANCE_METRICS]: {
    // ... other properties
    titleGenerator: (filters) => {
        const branchName = filters?.branchName || 'All Branches';
        return `Performance Metrics - ${branchName}`;
    }
}
```

## 🧪 Testing New Charts

### **Unit Test Template**
```typescript
// In dashboard.service.test.ts
describe('New Chart: Performance Metrics', () => {
    it('should include performance metrics chart in response', async () => {
        const widgets = await getUserDashboardWidgets('org-123', 456, { 
            widgetType: 'charts',
            chartType: 'line'
        });
        
        const performanceChart = widgets.find(w => w.id === 9);
        expect(performanceChart).toBeDefined();
        expect(performanceChart?.title).toBe('Performance Metrics Trend');
        expect(performanceChart?.data.chartType).toBe('line');
    });

    it('should filter performance metrics when chartType filter applied', async () => {
        const widgets = await getUserDashboardWidgets('org-123', 456, { 
            widgetType: 'charts',
            chartType: 'bar'
        });
        
        const performanceChart = widgets.find(w => w.id === 9);
        expect(performanceChart).toBeUndefined(); // Should not be included in bar charts
    });
});
```

## 📊 Chart Removal Example

### **Temporarily Disable a Chart**
```typescript
// Option 1: Comment out configuration
const CHART_CONFIGURATIONS: Record<ChartType, ChartConfig> = {
    // [ChartType.ONBOARDING_LINE]: { ... }, // Temporarily disabled
    [ChartType.ONBOARDING_BAR]: { ... },
    // ... other charts
};

// Option 2: Add disabled flag
interface ChartConfig {
    // ... existing properties
    disabled?: boolean;
}

const getFilteredChartConfigs = (filters?: FilterOptions): ChartConfig[] => {
    let configs = Object.values(CHART_CONFIGURATIONS)
        .filter(config => !config.disabled); // Filter out disabled charts
    
    // ... rest of filtering logic
};
```

## 🚀 Benefits of This Approach

### **Before Adding New Chart (Old System)**
1. ❌ Modify hardcoded promise array
2. ❌ Add new hardcoded widget creation logic  
3. ❌ Update array indices in multiple places
4. ❌ Risk breaking existing charts
5. ❌ Duplicate widget creation code

### **After Adding New Chart (New System)**
1. ✅ Add enum value (1 line)
2. ✅ Create data fetcher function
3. ✅ Add configuration entry (1 object)
4. ✅ Automatic integration with existing system
5. ✅ Zero risk to existing charts

## 📈 Scalability Benefits

- **Easy Maintenance**: Single configuration change affects entire chart lifecycle
- **Consistent Behavior**: All charts follow same patterns automatically
- **Type Safety**: Compile-time validation prevents runtime errors
- **Performance**: Dynamic filtering reduces unnecessary data fetching
- **Extensibility**: Easy to add new features to all charts simultaneously

The refactored system makes adding new charts a **simple, safe, and scalable process** that maintains code quality and system reliability! 🎯
